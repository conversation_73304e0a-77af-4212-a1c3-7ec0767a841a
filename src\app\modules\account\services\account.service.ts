import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, Subject, map, of, tap } from 'rxjs';
import { ApiResponse, UserState } from 'src/app/shared/constant';

@Injectable({
  providedIn: 'root',
})
export class AccountService {
  isLoggedIn$ = new BehaviorSubject(false);
  viewType$ = new BehaviorSubject('reseller');
  profileImage$ = new BehaviorSubject<string>('');
  user$ = new BehaviorSubject<any>({
    userName: '',
    userId: '',
    aspNetId: '',
    userType: null,
    comanyName: '',
  });
  isLoggedIn: any;
  user: any = {};
  cookiesPrivacy$ = new Subject();
  constructor(private httpClient: HttpClient) {
    const user = localStorage.getItem('user');
    const cookies = localStorage.getItem('cookies');
    if (user) {
      this.isLoggedIn = true;
      this.user = JSON.parse(user);
      this.user$.next(JSON.parse(user));
    } else {
      this.user = {};
      this.isLoggedIn = false;
    }
    if (!cookies) {
      this.cookiesPrivacy$.next('partial');
    }
  }

  getRegistrationData(userType: string) {
    return this.httpClient.post(
      `RegisterUser/GetRegisterUserFormData?userType=${userType}`,
      null
    );
  }

  getTypeOfExpert(typeId: string) {
    return this.httpClient.get(
      `CompanySystem/GetCompanySystemLookUpByTypeId?typeId=${typeId}`
    );
  }

  getInstallationList() {
    return this.httpClient.get(
      `TypeOfInstallation/GetTypeOfInstallationLookUp`
    );
  }

  getStates(id: any) {
    return this.httpClient.get(`State/GetStateLookUp?countryId=${id}`);
  }

  getCities(id: any) {
    return this.httpClient.get(`City/GetCityLookUp?stateId=${id}`);
  }

  login(user: any) {
    return this.httpClient.post('login/LoginUser', user);
  }

  registerUser(payload: any) {
    return this.httpClient.post(`registeruser/register`, payload);
  }

  getCompanyDetails(companyId: string) {
    return this.httpClient.get(
      'company/getcompanydetail?compayId=' + companyId
    );
  }

  getAccountDetails(accountId: string, isCompany = false) {
    return this.httpClient.get(
      'User/GetUserDetail?userId=' + accountId + '&isCompany=' + isCompany
    );
  }

  getUserElevatorVideos(accountId: string) {
    return this.httpClient.get(
      `ApprovePost/GetUserApprovedPost?userId=${accountId}`
    );
  }

  getUsersApprovedElevatorPitches(accountId: string) {
    return this.httpClient.get(
      `ApprovePost/GetUserApprovedPost?userId=${accountId}`
    );
  }

  updateAccountDetail(user: any) {
    return this.httpClient.post('User/AddUpdateUser', user);
  }

  updateEndUser(endUSer: any) {
    return this.httpClient.post('EndUser/Edit', endUSer);
  }

  searchCompanyWebsite(term: string) {
    return this.httpClient.get('Company/GetCompanyListByTerm?term=' + term);
  }

  changeCurrentPassword(payload: any) {
    return this.httpClient.post(`User/UpdatePassword`, payload);
  }

  addToFavorite(payload: any) {
    return this.httpClient.post(
      `UserFavorite/AddRmoveUserFavorite?loginUserId=${payload.loginUserId}&expertId=${payload.expertId}&isAdd=${payload.isAdd}`,
      null
    );
  }

  getCompanyProfile(payload: { loginUserId: string; userId: string }) {
    return this.httpClient.post(`ECard/GetCompanyCardDetail`, payload);
  }

  getProfile(payload: { loginUserId: string; userId: string }) {
    return this.httpClient.post(`ECard/GetCardDetail`, payload);
  }

  saveDocuments(payload: any) {
    return this.httpClient.post('UserDocument/AddElevatorPitchVideo', payload);
  }

  getUserDocuments(userId: any) {
    return this.httpClient.get(
      `UserDocument/GetUserDocuments?userId=` + userId
    );
  }

  uploadProfileImage(payload: any) {
    return this.httpClient.post(
      `UserDocument/addupdateuserprofileimage`,
      payload
    );
  }

  uploadCompanyProfileImage(payload: any) {
    return this.httpClient.post(`UserDocument/AddUpdateCompanyImage`, payload);
  }

  getFavorites(payload: any) {
    return this.httpClient.get(
      `UserFavorite/GetUserFavoriteList?userId=${payload}`
    );
  }

  connectExpert(payload: any) {
    return this.httpClient.post(`Follower/sendConnectionRequest`, payload);
  }

  postComment(payload: any) {
    return this.httpClient.post(`userComment/addusercomment`, payload);
  }

  getCompanyComments(companyId: string) {
    return this.httpClient.get(
      `UserComment/GetUserComments?expertUserId=${companyId}`
    );
  }

  setAccountPreference(payload: any) {
    return this.httpClient.post(
      `UserNotificationSetting/AddUpdateUserNotifiactionSetting`,
      payload
    );
  }

  getAccountPreference(userId: any) {
    return this.httpClient.get(
      `UserNotificationSetting/GetUserNotificationSettings?userId=${userId}`
    );
  }

  searchEcard(searchTerm: string, loginUserId = null) {
    return this.httpClient.get(
      `Ecard/SearchCard?term=${searchTerm}&loginUserId=${loginUserId}`
    );
  }

  getFilters() {
    return this.httpClient.get(`ECard/GetFilterFormData`);
  }

  getRecoveryMail(email: string) {
    return this.httpClient.get(`Login/forgotPassword?email=${email}`);
  }

  setNewPassword(payload: any) {
    return this.httpClient.post(`Login/ResetPassword`, payload);
  }

  addCompanyRating(payload: any) {
    return this.httpClient.post(`UserComment/AddUpdateCommentRatting`, payload);
  }

  buldInvite(payload: any) {
    return this.httpClient.post(`BulkInvite/SendBulkInviteMail`, payload);
  }

  followingList(userId: string) {
    return this.httpClient.get(`Follower/GetUserFollowing?userId=${userId}`);
  }

  followersList(userId: string) {
    return this.httpClient.get(`Follower/GetUserFollowers?userId=${userId}`);
  }

  getUserDetails(userId: string) {
    return this.httpClient.get(`User/GetUserDetail?userId=${userId}`);
  }

  checkEmailOrPhoneNumber(params: any) {
    return this.httpClient.get(`RegisterUser/CheckEmailIsExist`, { params });
  }

  activateAccount(params: any) {
    return this.httpClient.get(`User/VerifyEmail`, { params });
  }

  addExpertUnderCompony(payload: any) {
    return this.httpClient.post(`User/AddUpdateUser`, payload);
  }

  uploadBanner(formdata: any, companyId: string) {
    return this.httpClient.post(
      `Company/AddCompanyBannerPhoto?companyId=${companyId}`,
      formdata
    );
  }

  uploadUserBanner(formData: any) {
    return this.httpClient.post(`UserDocument/AddUpdateUserBanner`, formData);
  }

  getCompanyExpertList(companyId: string): Observable<Object> {
    return this.httpClient.get(`Company/GetComapnyExpertList/${companyId}`);
  }

  resendVarificationMail(email: string) {
    return this.httpClient.post(
      `User/ResendVerificationMail?email=${email}`,
      {}
    );
  }

  getFilterEcards(payload: any) {
    return this.httpClient.post(`ECard/GetFilteredECardList`, payload);
  }

  getUserNotifications(userId: string) {
    return this.httpClient.get(
      `UserNotification/GetUserNotifications?userId=${userId}`
    );
  }

  getUserUnreadNotificationCount(userId: string) {
    return this.httpClient.get(
      `UserNotification/GetUserNotificationsUnReadCount?userId=${userId}`
    );
  }

  markAsReadNotification(notificationId: any) {
    return this.httpClient.post(
      `UserNotification/ReadNotification?id=${notificationId}`,
      {}
    );
  }

  getTechnologies() {
    return this.httpClient.get(`Technology/GetTechnologyLookUp`);
  }

  // update user from admin side
  updateUserFromAdmin(payload = null) {
    return this.httpClient.post('user/AddUpdateUserAdmin', payload);
  }

  getCountries() {
    return this.httpClient.get('Country/GetCountryLookUp');
  }

  getRecentlyJoinedPartners(id: any) {
    return this.httpClient.get(
      'ECard/GetRecentlyJoinedPartners?solutionId=' + id
    );
  }

  addUserElevatorPitch(payload: any) {
    return this.httpClient.post(`ApprovePost/AddPost`, payload);
  }

  updateUserElevatorPitch(payload: any) {
    return this.httpClient.post(`ApprovePost/updatePost`, payload);
  }

  getCompanyElevatorPitch(id: any) {
    return this.httpClient.get(
      `ApprovePost/GetCompanyElevatorPost?adminid=${id}`
    );
  }

  saveSocialMedia(payload: any) {
    return this.httpClient.post(
      `UserSocialConnection/AddUpdateUserConnection`,
      payload
    );
  }

  getSocialMedia(id: any) {
    return this.httpClient.get(
      `UserSocialConnection/GetUserSocialConnections?userId=${id}`
    );
  }

  adminRegistration(payload: any) {
    return this.httpClient.post(`RegisterUser/RegisterPartially`, payload);
  }

  getOrganizationType() {
    return this.httpClient.get('OrganizationType/Get');
  }
  getApprovedConnection() {
    return this.httpClient.get(`UserFavorite/MyConnections`);
  }

  // Get approved connections by user type
  getApprovedConnectionsByType(userType: number) {
    return this.httpClient.get(`UserFavorite/MyConnections/${userType}`);
  }
  getCompanyRatingData(userId: string): Observable<any> {
    return this.httpClient.get(`ECard/GetCompanyRatingData?userId=${userId}`);
  }
  getTestimonials() {
    return this.httpClient.get('Testimonials/GetTestimonials');
  }

  uploadeCardBanner(formdata: any, companyId: string) {
    return this.httpClient.post(
      `Company/AddUpdateCompanyEcardBanner?companyId=${companyId}`,
      formdata
    );
  }
  getCompanyEcardBanner(companyId: string): Observable<any> {
    return this.httpClient.get(
      `Company/GetCompanyEcardBanner?companyId=${companyId}`
    );
  }

  getConnectionAndReferrals(
    pageNumber: number,
    pageSize: number
  ): Observable<any> {
    return this.httpClient.get(
      `EndUser/GetConnectionAndReferrals?pageNumber=${pageNumber}&pageSize=${pageSize}`
    );
  }
  getMyBusiness() {
    return this.httpClient.get('User/GetMyBusiness');
  }
  getFrequentlyAskedQuestions(): Observable<any> {
    return this.httpClient.get(
      'FrequentlyAskedQuestion/GetFrequentlyAskedQuestion'
    );
  }

  getAddFavorite(expertID: string): Observable<any> {
    return this.httpClient.post<any>(
      `UserFavorite/AddFavorite?expertId=${expertID}`,
      {}
    );
  }

  getRemoveFavorite(expertID: string): Observable<any> {
    return this.httpClient.delete<any>(
      `UserFavorite/RemoveFavorite?expertId=${expertID}`
    );
  }


  getFavoritesByType(companyType: number): Observable<any> {
    return this.httpClient.get(`UserFavorite/GetFavorites?Type=${companyType}`);
  }

  getPendingRequestList(userId: string) {
    return this.httpClient.get(
      `Follower/GetUserRequestedList?userId=${userId}`
    );
  }

  // Get pending requests by user type
  getPendingRequestsByType(userType: number) {
    return this.httpClient.get(`Follower/GetUserRequestedList/${userType}`);
  }
  requestDemo(payload: any) {
    return this.httpClient.post(`BulkInvite/RequestDemo`, payload);
  }

  getFrequentlyAskedQuestionsByType(type: number): Observable<any> {
    return this.httpClient
      .get(`FrequentlyAskedQuestion/GetFrequentlyAskedQuestionByType?type=${type}
`);
  }

  getEndUserDetails(userId: string): Observable<any> {
    return this.httpClient.get(`EndUser/details/${userId}`);
  }

  getUserSettings(): Observable<any> {
    return this.httpClient.get('User/GetSettings');
  }

  // Blog API methods
  getAllBlogs(): Observable<any> {
    return this.httpClient.get('Blog/All');
  }

  getAllBlogCategories(): Observable<any> {
    return this.httpClient.get('Blog/GetAllCategories');
  }

  getBlogBySlug(slug: string): Observable<any> {
    return this.httpClient.get(`Blog/${slug}`);
  }

  // Blog like/unlike functionality
  likeBlog(blogId: string): Observable<any> {
    return this.httpClient.post(`Blog/${blogId}/like`, {});
  }

  unlikeBlog(blogId: string): Observable<any> {
    return this.httpClient.delete(`Blog/${blogId}/like`);
  }

  // Blog view count
  incrementBlogView(blogId: string): Observable<any> {
    return this.httpClient.post(`Blog/${blogId}/view`, {});
  }

  // Subscription Plans
  getSubscriptionPlans(): Observable<any> {
    return this.httpClient.get('Subscription/SubscriptionPlans');
  }

  getServiceActivities(userId: string) {
    return this.httpClient.get<any>(
      `ServiceActivities/GetAll/${userId}`
    );
  }

  addActivityComment(payload: any) {
    return this.httpClient.post('ActivityComment/Add', payload);
  }

  getActivityComments(activityId: string) {
    return this.httpClient.get<any[]>(`ActivityComment/${activityId}`);
  }

  createPost(payload: any) {
    return this.httpClient.post('ServiceActivities/Add', payload);
  }

  editPost(postId: string, payload: any) {
    return this.httpClient.put(`ServiceActivities/edit/${postId}`, payload);
  }

  // Service Activities Like/Unlike functionality
  addOrRemovePostLike(payload: any) {
    return this.httpClient.post('ServiceActivities/AddOrRemovePostLike', payload);
  }

  likePost(postId: string) {
    return this.httpClient.post(`ServiceActivities/${postId}/like`, {});
  }

  unlikePost(postId: string) {
    return this.httpClient.delete(`ServiceActivities/${postId}/like`);
  }

}