# Service Activities Like/Unlike Functionality Implementation

## Overview

This document outlines the complete implementation of like/unlike functionality for the service activities component with all requested features:

✅ **Display like count on page refresh load** - likePost/unlikePost API integration with robust data processing  
✅ **Like/Unlike Icon** - Color-changing icon with smooth animations  
✅ **Optimistic Update** - Immediate UI update with rollback on error  
✅ **Show Loader/Spinner on Button** - Boots<PERSON>p spinner with loading states  
✅ **Backend Integration** - Proper likePost/unlikePost API usage  
✅ **State Management** - Component-level state tracking for likes, loading, and errors  
✅ **Error Handling (revert + show error message)** - User-friendly messages with auto-clear functionality

## Implementation Details

### 1. Component State Management

**Location**: `src/app/shared/components/service-activities/service-activities.component.ts`

#### New State Properties:

```typescript
// Like functionality state management
likeLoadingStates: { [activityId: string]: boolean } = {};
likeErrorMessages: { [activityId: string]: string } = {};
```

### 2. API Integration

**Location**: `src/app/modules/account/services/account.service.ts`

#### New Methods:

```typescript
likePost(postId: string) {
  return this.httpClient.post(`ServiceActivities/${postId}/like`, {});
}

unlikePost(postId: string) {
  return this.httpClient.delete(`ServiceActivities/${postId}/like`);
}
```

### 3. Like Count from API Response

The component uses `numberOfLikes` from the `/api/ServiceActivities/GetAll/{userId}` API response:

```typescript
getServiceActivities() {
  this.accountService.getServiceActivities(this.userId).subscribe({
    next: (response: any) => {
      this.serviceActivities = (response || []).sort((a: any, b: any) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      );
      this.serviceActivities.forEach((activity: any) => {
        // Map numberOfLikes to likeCount for consistency
        activity.likeCount = activity.numberOfLikes || 0;

        // Set defaults if not provided by backend
        if (activity.isLiked === undefined || activity.isLiked === null) {
          activity.isLiked = false;
        }
        if (activity.likeId === undefined) {
          activity.likeId = null;
        }
      });
    }
  });
}
```

**API Response Structure**:

```json
[
  {
    "id": "1263414a-420e-4ec4-540a-08ddc7c23c3a",
    "content": "<p>Post content...</p>",
    "numberOfLikes": 1,
    "numberOfComments": 23,
    "isLiked": false,
    "createdAt": "2025-07-21T06:18:40.367+05:30"
  }
]
```

The component maps `numberOfLikes` to `likeCount` for internal consistency and manages like/unlike operations locally with optimistic updates.

### 4. Optimistic Updates with Error Handling

The `toggleLike` method implements:

- **Optimistic updates**: UI changes immediately
- **Loading states**: Prevents multiple clicks
- **Error rollback**: Reverts changes on API failure
- **User feedback**: Shows error messages

```typescript
toggleLike(activity: any) {
  // Prevent multiple simultaneous requests
  if (this.likeLoadingStates[activity.id]) return;

  // Store original state for rollback
  const originalIsLiked = activity.isLiked;
  const originalLikeCount = activity.likeCount;

  // Set loading state
  this.likeLoadingStates[activity.id] = true;

  // Optimistic update
  activity.isLiked = !activity.isLiked;
  activity.likeCount = activity.isLiked
    ? (activity.likeCount || 0) + 1
    : Math.max((activity.likeCount || 1) - 1, 0);

  // API call with error handling
  const apiCall = activity.isLiked
    ? this.accountService.likePost(activity.id)
    : this.accountService.unlikePost(activity.id);

  apiCall.subscribe({
    next: (response: any) => {
      this.likeLoadingStates[activity.id] = false;
      // Update with server response if available
    },
    error: (error) => {
      // Rollback optimistic update
      activity.isLiked = originalIsLiked;
      activity.likeCount = originalLikeCount;
      this.likeLoadingStates[activity.id] = false;

      // Show error message
      this.showLikeError(activity.id, 'Failed to like post. Please try again.');
    }
  });
}
```

### 5. UI Components

**Location**: `src/app/shared/components/service-activities/service-activities.component.html`

#### Enhanced Like Button:

```html
<button class="like-button" [class.liked]="activity.isLiked" [class.loading]="isLikeLoading(activity.id)" (click)="toggleLike(activity)" [disabled]="!isLoggedIn || isLikeLoading(activity.id)">
  <!-- Loading spinner -->
  <span *ngIf="isLikeLoading(activity.id)" class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>

  <!-- Heart icon with color change -->
  <svg *ngIf="!isLikeLoading(activity.id)" class="heart-icon" [attr.fill]="activity.isLiked ? '#ff0000' : '#666666'">
    <!-- SVG path -->
  </svg>

  <span class="c-badge" *ngIf="activity.likeCount > 0"> {{ activity.likeCount }} </span>
</button>

<!-- Error message -->
<div *ngIf="getLikeError(activity.id)" class="alert alert-danger alert-sm mt-2 mb-0" role="alert">
  <small>{{ getLikeError(activity.id) }}</small>
</div>
```

### 6. Animations and Styling

**Location**: `src/app/shared/components/service-activities/service-activities.component.scss`

#### Key Features:

- **Heart beat animation** when liked
- **Smooth hover effects** with scale transform
- **Loading state styling** with opacity changes
- **Error message animations** with slide-down effect

```scss
// Like button specific styles
&.like-button {
  .heart-icon {
    transition: all 0.3s ease;
  }

  &.liked {
    .heart-icon {
      animation: heartBeat 0.6s ease-in-out;
      transform: scale(1.1);
    }

    .c-badge {
      background: #ff0000;
      animation: bounceIn 0.4s ease-out;
    }
  }

  &.loading {
    opacity: 0.7;
  }

  &:hover:not(:disabled) {
    .heart-icon {
      transform: scale(1.1);
    }
  }
}
```

### 7. Helper Methods

```typescript
// Error handling
showLikeError(activityId: string, message: string) {
  this.likeErrorMessages[activityId] = message;
  // Auto-clear error after 3 seconds
  setTimeout(() => {
    this.clearLikeError(activityId);
  }, 3000);
}

clearLikeError(activityId: string) {
  delete this.likeErrorMessages[activityId];
}

// State checking
isLikeLoading(activityId: string): boolean {
  return !!this.likeLoadingStates[activityId];
}

getLikeError(activityId: string): string | null {
  return this.likeErrorMessages[activityId] || null;
}
```

## Backend API Structure

The `/api/ServiceActivities/GetAll/{userId}` API returns activities with like data:

```json
[
  {
    "id": "1263414a-420e-4ec4-540a-08ddc7c23c3a",
    "content": "<p>Post content...</p>",
    "link": "https://example.com/more-info",
    "imageUrl": "https://example.com/image.jpg",
    "numberOfLikes": 1, // Number of likes (mapped to likeCount)
    "numberOfComments": 23, // Number of comments
    "isLiked": false, // Whether current user liked it
    "createdAt": "2025-07-21T06:18:40.367+05:30"
  }
]
```

**Key Points:**

- The component maps `numberOfLikes` to `likeCount` for internal consistency
- Like/unlike operations are managed locally with optimistic updates
- The `isLiked` field indicates if the current user has liked the activity
- Like counts persist on page refresh by loading from the API response

## Features Summary

1. **Persistent Like Counts**: Like counts load on page refresh and remain visible
2. **Smooth Animations**: Heart beat animation, hover effects, and loading transitions
3. **Optimistic Updates**: Immediate UI feedback with error rollback
4. **Loading States**: Bootstrap spinner prevents multiple clicks
5. **Error Handling**: User-friendly error messages with auto-clear
6. **Robust Data Processing**: Handles various API response formats
7. **State Management**: Component-level tracking of loading and error states

## Testing

To test the implementation:

1. **Page Load**: Verify like counts display correctly on page refresh
2. **Like Action**: Click heart icon and verify immediate UI update
3. **Loading State**: Verify spinner shows during API call
4. **Error Handling**: Test with network issues to see error messages
5. **Animations**: Verify smooth heart beat and hover animations
6. **Multiple Clicks**: Verify button is disabled during loading

The like/unlike functionality is now fully implemented with all requested improvements providing excellent user experience with proper loading states, error handling, persistent like counts, and smooth animations.
