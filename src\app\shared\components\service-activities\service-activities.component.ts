import { Component, Input, OnInit } from '@angular/core';
import { AccountService } from 'src/app/modules/account/services/account.service';
import { ModalService } from 'src/app/shared/services/modal.service';
import { forkJoin } from 'rxjs';
import { EMPTY_GUID } from '../../constant';

@Component({
    selector: 'app-service-activities',
    templateUrl: './service-activities.component.html',
    styleUrls: ['./service-activities.component.scss']
})
export class ServiceActivitiesComponent implements OnInit {
    @Input() userId: string = '';
    @Input() userProfilePhoto: string = '';
    @Input() userName: string = '';
    @Input() userRole: string = '';
    @Input() isLoggedIn: boolean = false;
    @Input() currentUserId: string = '';
    @Input() currentUserPhoto: string = '';
    @Input() currentUserName: string = '';

    serviceActivities: any[] = [];
    serviceActivitiesLoading = false;
    commentInputs: { [activityId: string]: string } = {};
    showAllComments: { [activityId: string]: boolean } = {};

    // Like functionality state management
    likeLoadingStates: { [activityId: string]: boolean } = {};
    likeErrorMessages: { [activityId: string]: string } = {};

    constructor(
        private accountService: AccountService,
        private modalService: ModalService
    ) { }

    ngOnInit(): void {
        if (this.userId) {
            this.getServiceActivities();
        }
    }

    getServiceActivities() {
        this.serviceActivitiesLoading = true;
        this.accountService.getServiceActivities(this.userId).subscribe({
            next: (response: any) => {
                this.serviceActivities = (response || []).sort((a: any, b: any) =>
                    new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
                );
                this.serviceActivities.forEach((activity: any) => {
                    // Map numberOfLikes to likeCount for consistency
                    activity.likeCount = activity.numberOfLikes || 0;

                    // Set defaults if not provided by backend
                    if (activity.isLiked === undefined || activity.isLiked === null) {
                        activity.isLiked = false;
                    }
                    if (activity.likeId === undefined) {
                        activity.likeId = null;
                    }
                });
                this.loadCommentsAndLikesForActivities();
            },
            error: (err) => {
                this.serviceActivities = [];
                this.serviceActivitiesLoading = false;
            }
        });
    }

    loadCommentsAndLikesForActivities() {
        if (this.serviceActivities.length === 0) {
            this.serviceActivitiesLoading = false;
            return;
        }

        const commentRequests = this.serviceActivities.map(activity =>
            this.accountService.getActivityComments(activity.id)
        );

        forkJoin(commentRequests).subscribe({
            next: (commentsResponses: any[]) => {
                this.serviceActivities.forEach((activity, index) => {
                    // Handle comments
                    activity.comments = commentsResponses[index] || [];
                    activity.commentCount = activity.comments.length;

                    // Map numberOfLikes to likeCount if not already set
                    if (activity.likeCount === undefined || activity.likeCount === null) {
                        activity.likeCount = activity.numberOfLikes || 0;
                    }
                    if (activity.isLiked === undefined || activity.isLiked === null) {
                        activity.isLiked = false;
                    }
                    if (activity.likeId === undefined) {
                        activity.likeId = null;
                    }

                    activity.comments.forEach((comment: any) => {
                        if (comment.userId === this.currentUserId) {
                            if (!comment.userProfilePhoto && this.currentUserPhoto) {
                                comment.userProfilePhoto = this.currentUserPhoto;
                            }
                            if (!comment.userName && this.currentUserName) {
                                comment.userName = this.currentUserName;
                            }
                        }
                    });

                    activity.comments.sort((a: any, b: any) =>
                        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
                    );
                });
                this.serviceActivitiesLoading = false;
            },
            error: (err) => {
                console.error('Error loading comments:', err);
                this.serviceActivities.forEach((activity: any) => {
                    if (!activity.comments) activity.comments = [];
                    if (activity.likeCount === undefined) activity.likeCount = activity.numberOfLikes || 0;
                    if (activity.isLiked === undefined) activity.isLiked = false;
                    if (activity.likeId === undefined) activity.likeId = null;
                });
                this.serviceActivitiesLoading = false;
            }
        });
    }



    toggleLike(activity: any) {
        if (!this.isLoggedIn || !this.currentUserId) return;

        // Prevent multiple simultaneous requests
        if (this.likeLoadingStates[activity.id]) return;

        // Clear any previous error messages
        this.clearLikeError(activity.id);

        // Store original state for rollback
        const originalIsLiked = activity.isLiked;
        const originalLikeCount = activity.likeCount;
        const originalLikeId = activity.likeId;

        // Set loading state
        this.likeLoadingStates[activity.id] = true;

        // Optimistic update
        activity.isLiked = !activity.isLiked;
        activity.likeCount = activity.isLiked
            ? (activity.likeCount || 0) + 1
            : Math.max((activity.likeCount || 1) - 1, 0);

        // Prepare payload for addOrRemovePostLike API
        const payload = {
            postId: activity.id,
            userId: this.currentUserId,
            isLike: activity.isLiked
        };

        // Call addOrRemovePostLike API
        this.accountService.addOrRemovePostLike(payload).subscribe({
            next: (response: any) => {
                // Success - keep optimistic update
                this.likeLoadingStates[activity.id] = false;

                // Update with server response if available
                if (response && response.likeId) {
                    activity.likeId = response.likeId;
                }
                if (response && response.likeCount !== undefined) {
                    activity.likeCount = response.likeCount;
                }
                if (response && response.numberOfLikes !== undefined) {
                    activity.likeCount = response.numberOfLikes;
                }
            },
            error: (error) => {
                // Log error details for debugging
                console.error('Like/Unlike API Error:', error);
                console.error('Activity ID:', activity.id);
                console.error('Current User ID:', this.currentUserId);
                console.error('Payload:', payload);
                console.error('Original state:', { originalIsLiked, originalLikeCount, originalLikeId });

                // Rollback optimistic update
                activity.isLiked = originalIsLiked;
                activity.likeCount = originalLikeCount;
                activity.likeId = originalLikeId;
                this.likeLoadingStates[activity.id] = false;

                // Show error message with more details
                const errorMessage = error?.error?.message || error?.message || 'Failed to update like status. Please try again.';
                this.showLikeError(activity.id, errorMessage);
            }
        });
    }

    // Helper methods for error handling
    showLikeError(activityId: string, message: string) {
        this.likeErrorMessages[activityId] = message;
        // Auto-clear error after 3 seconds
        setTimeout(() => {
            this.clearLikeError(activityId);
        }, 3000);
    }

    clearLikeError(activityId: string) {
        delete this.likeErrorMessages[activityId];
    }

    // Check if like button is loading
    isLikeLoading(activityId: string): boolean {
        return !!this.likeLoadingStates[activityId];
    }

    // Get error message for activity
    getLikeError(activityId: string): string | null {
        return this.likeErrorMessages[activityId] || null;
    }





    postComment(activity: any) {
        if (!this.isLoggedIn || !this.currentUserId || !this.commentInputs[activity.id]?.trim()) {
            return;
        }

        const payload = {
            activityId: activity.id,
            userId: this.currentUserId,
            content: this.commentInputs[activity.id]
        };



        const commentText = this.commentInputs[activity.id];

        this.accountService.addActivityComment(payload).subscribe({
            next: (res: any) => {
                this.accountService.getActivityComments(activity.id).subscribe({
                    next: (commentsResponse: any[]) => {

                        activity.comments = commentsResponse || [];
                        activity.commentCount = activity.comments.length;

                        activity.comments.forEach((comment: any) => {
                            if (comment.userId === this.currentUserId) {
                                if (!comment.userProfilePhoto && this.currentUserPhoto) {
                                    comment.userProfilePhoto = this.currentUserPhoto;
                                }
                                if (!comment.userName && this.currentUserName) {
                                    comment.userName = this.currentUserName;
                                }
                            }
                        });
                        activity.comments.sort((a: any, b: any) =>
                            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
                        );
                        this.commentInputs[activity.id] = '';
                    },
                    error: (err) => {
                        const newComment = {
                            userId: this.currentUserId,
                            content: commentText,
                            createdAt: new Date(),
                            userName: this.currentUserName || this.userName || 'User',
                            userProfilePhoto: this.currentUserPhoto || this.userProfilePhoto || './assets/images/default-avatar.png'
                        };
                        activity.comments = activity.comments || [];
                        activity.comments.unshift(newComment);
                        activity.commentCount = activity.comments.length;
                        this.commentInputs[activity.id] = '';
                    }
                });
            },
            error: (err) => {
                const newComment = {
                    userId: this.currentUserId,
                    content: commentText,
                    createdAt: new Date(),
                    userName: this.currentUserName || this.userName || 'User',
                    userProfilePhoto: this.currentUserPhoto || this.userProfilePhoto || './assets/images/default-avatar.png'
                };
                activity.comments = activity.comments || [];
                activity.comments.unshift(newComment);
                this.commentInputs[activity.id] = '';
            }
        });
    }

    onCommentKeyDown(event: KeyboardEvent, activity: any) {
        if (event.key === 'Enter' && this.commentInputs[activity.id]?.trim()) {
            this.postComment(activity);
        }
    }

    createPost() {
        if (!this.isLoggedIn || !this.currentUserId) {
            return;
        }

        const modalData = {
            initialState: {
                isEditMode: false,
                postData: null,
                userId: this.currentUserId,
                userProfilePhoto: this.userProfilePhoto,
                userName: this.userName
            }
        };

        const modalRef = this.modalService.openModal('create-edit-post', modalData);

        if (modalRef) {
            modalRef.content?.onClose?.subscribe((result: any) => {
                if (result && result.success) {
                    this.getServiceActivities();
                }
            });
        }
    }

    editPost(activity: any) {
        if (!this.canEditPost(activity)) {
            return;
        }

        const modalData = {
            initialState: {
                isEditMode: true,
                postData: activity,
                userId: this.currentUserId,
                userProfilePhoto: this.userProfilePhoto,
                userName: this.userName
            }
        };

        const modalRef = this.modalService.openModal('create-edit-post', modalData);

        if (modalRef) {
            modalRef.content?.onClose?.subscribe((result: any) => {
                if (result && result.success) {
                    this.getServiceActivities();
                }
            });
        }
    }

    canEditPost(activity: any): boolean {

        if (activity.userId) {
            return !!(this.isLoggedIn && this.currentUserId &&
                activity.userId.toLowerCase() === this.currentUserId.toLowerCase());
        }
        const isOwnProfile = !!(this.isLoggedIn && this.currentUserId && this.userId &&
            this.userId.toLowerCase() === this.currentUserId.toLowerCase());
        return isOwnProfile;
    }

    isOwnProfile(): boolean {
        const isOwn = !!(this.isLoggedIn && this.currentUserId && this.userId &&
            this.userId.toLowerCase() === this.currentUserId.toLowerCase());
        return isOwn;
    }

    getDisplayName(activity: any): string {
        // Try activity data first
        if (activity.userName && activity.userName.trim()) {
            return activity.userName;
        }

        // Try firstName + lastName from activity
        if (activity.firstName || activity.lastName) {
            const firstName = activity.firstName || '';
            const lastName = activity.lastName || '';
            const fullName = `${firstName} ${lastName}`.trim();
            if (fullName) {
                return fullName;
            }
        }

        // Fallback to input parameters (profile data)
        return this.userName || 'User';
    }

    getDisplayRole(activity: any): string {
        // Try activity data first
        if (activity.userRole && activity.userRole.trim()) {
            return activity.userRole;
        }

        if (activity.roleName && activity.roleName.trim()) {
            return activity.roleName;
        }

        return this.userRole || '';
    }

    getDisplayPhoto(activity: any): string {
        // Try activity data first
        if (activity.userProfilePhoto && activity.userProfilePhoto.trim()) {
            return activity.userProfilePhoto;
        }

        if (activity.profilePhoto && activity.profilePhoto.trim()) {
            return activity.profilePhoto;
        }

        return this.userProfilePhoto || './assets/images/default-avatar.png';
    }

    getCommentDisplayName(comment: any): string {
        // Try comment data first
        if (comment.userName && comment.userName.trim()) {
            return comment.userName;
        }

        // Try firstName + lastName from comment
        if (comment.firstName || comment.lastName) {
            const firstName = comment.firstName || '';
            const lastName = comment.lastName || '';
            const fullName = `${firstName} ${lastName}`.trim();
            if (fullName) {
                return fullName;
            }
        }

        if (comment.userId === this.currentUserId) {
            if (this.currentUserName && this.currentUserName.trim()) {
                return this.currentUserName;
            }
            // Fallback to profile name if current user name not available
            if (this.userName && this.userName.trim()) {
                return this.userName;
            }
        }

        // Default fallback
        return 'User';
    }

    getCommentDisplayPhoto(comment: any): string {
        if (comment.userProfilePhoto && comment.userProfilePhoto.trim()) {
            return comment.userProfilePhoto;
        }

        if (comment.profilePhoto && comment.profilePhoto.trim()) {
            return comment.profilePhoto;
        }

        if (comment.userId === this.currentUserId) {
            if (this.currentUserPhoto && this.currentUserPhoto.trim()) {
                return this.currentUserPhoto;
            }
            if (this.userProfilePhoto && this.userProfilePhoto.trim()) {
                return this.userProfilePhoto;
            }
        }
        return './assets/images/default-avatar.png';
    }

    getDisplayedComments(activity: any): any[] {
        if (!activity.comments) return [];

        const shouldShowAll = this.showAllComments[activity.id];
        return shouldShowAll ? activity.comments : activity.comments.slice(0, 3);
    }

    shouldShowViewAllLink(activity: any): boolean {
        return activity.comments && activity.comments.length > 3 && !this.showAllComments[activity.id];
    }

    shouldShowViewLessLink(activity: any): boolean {
        return activity.comments && activity.comments.length > 3 && this.showAllComments[activity.id];
    }

    toggleShowAllComments(activity: any): void {
        this.showAllComments[activity.id] = !this.showAllComments[activity.id];
    }

    testModal() {
        const modalRef = this.modalService.openModal('confirmation', {
            initialState: {
                title: 'Test Modal',
                message: 'This is a test modal to verify the modal service is working.',
                firstButtonText: 'OK',
                onConfirm: () => {
                    this.modalService.closeModal();
                }
            }
        });
    }
}